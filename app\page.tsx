'use client';

import { useRouter } from 'next/navigation';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Header } from '@/components/header';
import Image from 'next/image';
import {
  Upload,
  MessageCircle,
  Shirt,
  Crown,
  Zap,
  ArrowRight,
  Star
} from 'lucide-react';

export default function Home() {
  const router = useRouter();

  const categories = [
    { name: 'Dress<PERSON>', icon: Crown, message: "I want to design a custom dress" },
    { name: 'T-Shirts', icon: Shirt, message: "I want to design a custom t-shirt" },
    { name: 'Digital Shorts', icon: Zap, message: "I want to design custom digital shorts" },
    { name: 'Pan<PERSON>', icon: Star, message: "I want to design custom pants" },
  ];

  const navigateToChat = (message?: string) => {
    if (message) {
      localStorage.setItem('initial-message', message);
    }
    router.push('/chat');
  };

  return (
    <div className="min-h-screen space-bg relative overflow-hidden">
      {/* Floating planets/decorative elements */}
      <div className="absolute top-20 left-8 w-16 h-16 rounded-full bg-gradient-to-br from-purple-400/30 to-pink-400/30 floating-planet"></div>
      <div className="absolute top-32 right-12 w-12 h-12 rounded-full bg-gradient-to-br from-orange-400/40 to-red-400/40 floating-planet-2"></div>
      <div className="absolute top-64 left-16 w-8 h-8 rounded-full bg-gradient-to-br from-blue-400/30 to-cyan-400/30 floating-planet-3"></div>

      <Header />

      <main className="pt-20 px-4 pb-8">
        <div className="max-w-sm mx-auto">
          {/* Logo */}
          <div className="text-center mb-8">
            <Image
              src="/logo.png"
              alt="Unique U Logo"
              width={60}
              height={60}
              className="mx-auto mb-4"
            />
          </div>

          {/* Welcome Section */}
          <div className="text-center mb-8">
            <h1 className="text-3xl font-bold text-white mb-2">
              Welcome
            </h1>
            <h2 className="text-3xl font-bold text-white mb-4">
              to Unique U
            </h2>
            <p className="text-white/80 text-lg">
              Smart fashion, with AI and You.
            </p>
          </div>

          {/* Main Action Cards */}
          <div className="space-y-4 mb-8">
            <Card
              className="bg-gradient-to-r from-orange-100 to-pink-100 border-0 cursor-pointer hover:shadow-lg transition-all duration-300 hover:scale-105"
              onClick={() => navigateToChat()}
            >
              <CardContent className="p-6">
                <div className="flex items-center space-x-4">
                  <div className="w-12 h-12 rounded-lg bg-white/80 flex items-center justify-center">
                    <Upload className="h-6 w-6 text-orange-600" />
                  </div>
                  <div className="flex-1">
                    <h3 className="font-semibold text-gray-800 text-lg">Upload</h3>
                    <h4 className="font-semibold text-gray-800 text-lg">Your Idea</h4>
                    <p className="text-gray-600 text-sm">Let's make it real</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card
              className="bg-gradient-to-r from-purple-100 to-blue-100 border-0 cursor-pointer hover:shadow-lg transition-all duration-300 hover:scale-105"
              onClick={() => navigateToChat("Hi I'm Aurora. Have a look in mind? Or have a picture? Let's create something uniquely yours!")}
            >
              <CardContent className="p-6">
                <div className="flex items-center space-x-4">
                  <div className="w-12 h-12 rounded-lg bg-white/80 flex items-center justify-center">
                    <MessageCircle className="h-6 w-6 text-purple-600" />
                  </div>
                  <div className="flex-1">
                    <h3 className="font-semibold text-gray-800 text-lg">Chat with</h3>
                    <h4 className="font-semibold text-gray-800 text-lg">Aurora</h4>
                    <p className="text-gray-600 text-sm">and style your look</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Categories Section */}
          <div className="mb-8">
            <p className="text-white/80 text-center mb-4">
              Not sure? Start exploring designs
            </p>

            <div className="grid grid-cols-2 gap-3">
              {categories.map((category) => (
                <Button
                  key={category.name}
                  variant="outline"
                  className="bg-white/10 border-white/20 text-white hover:bg-white/20 hover:text-white py-3 h-auto"
                  onClick={() => navigateToChat(category.message)}
                >
                  <category.icon className="h-4 w-4 mr-2" />
                  {category.name}
                </Button>
              ))}
            </div>
          </div>

          {/* Get Started Button */}
          <div className="text-center">
            <Button
              size="lg"
              className="w-full bg-gradient-to-r from-orange-500 to-pink-500 hover:from-orange-600 hover:to-pink-600 text-white font-semibold py-4 rounded-full shadow-lg hover:shadow-xl transition-all duration-300"
              onClick={() => navigateToChat()}
            >
              Get Started
              <ArrowRight className="ml-2 h-5 w-5" />
            </Button>
          </div>
        </div>
      </main>
    </div>
  );
}